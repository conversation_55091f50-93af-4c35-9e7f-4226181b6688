《统计全球每年的最高气温和最低气温》实训说明书
专业：人工智能       班级：人工智能
姓名		学号	
实训题目	统计全球每年的最高气温和最低气温、筛选
设计思路及内容概述
一、实训目的
麦肯锡这家公司指出：“数据，已经渗透到当今每一个行业和业务职能领域，成为重要的生产因素，人们对于海量数据的挖掘和运用，预示着新一波生产率增长和消费者盈余浪潮的到来”。在现在的很多领域中，都会产生数据，而数据量之大无法想象。若用原来的技术去做，总有种捉襟见肘的感觉，要么在性能上面，要么在速度上面遇到了瓶颈，这个时候就需要新的技术来解决，我们能想到的，比如用高并发，1M的数据单机来处理就够了，如果1000M数据，一台机子一个节点就可能做不了；也有可能考虑集群、分布式系统。但是分布式系统是很难编写的，要考虑线程、进程、网络通信等等很多问题。业务逻辑本来很简单，但因为考虑到分布式系统的协调问题，程序变得非常复杂，在传统的技术上花费太大。比如 count()，order by的业务，几百兆Mysql可以搞定，几十TB，Mysql和Oracle都扛不住。
于是hadoop框架就出现了，就像struts因为MVC而出现一样。hadoop是个框架，为了解决一个特定领域的问题而出现，这个特定领域就是海量数据处理。
二、 需求说明
 随着社会经济的发展，气候也在慢慢的发生变化。全球变暖将给地球和人类带来复杂的潜在的影响。每个国家每年的温度是不同的，这就意味着全球的温度数据量是非常庞大的。为实现统计全球每年的最高气温和最低气温的功能，我们采用大数据Hadoop技术来实现。

三、实现思路和步骤

四、实现代码
1.Mapper类实现代码：

2.Combiner类实现代码：

3.Reducer类实现代码：

4.驱动类实现代码：


五、相关截图
1、环境搭建成功的截图（包括以下五个截图）
1）、





2）、http://master:50070/dfshealth.html#tab-overview


3）、http://master:50070/dfshealth.html#tab-datanode



4)、http://master:50070/explorer.html#/



5)、http://master:8088/cluster/nodes

2、mapreduce任务的运行状态截图


3、运行过程中，命令行窗口的截图
1）

2、在终端定位到temperature文件目录
执行如下操作后即可显示结果。

3、成功文件与操作结果


六、小结

总结与收获




成绩		教师签名	      


2023年 12 月 日